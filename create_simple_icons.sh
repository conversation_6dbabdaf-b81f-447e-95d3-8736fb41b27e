#!/bin/bash

# Create simple PNG icons using ImageMagick (if available) or create placeholder files

create_icon() {
    local size=$1
    local filename=$2
    
    if command -v convert >/dev/null 2>&1; then
        # Use ImageMagick to create a simple icon
        convert -size ${size}x${size} xc:'#4285f4' \
                -gravity center \
                -pointsize $((size/2)) \
                -fill white \
                -annotate +0+0 'J' \
                "$filename"
        echo "Created $filename using ImageMagick"
    else
        # Create a minimal PNG file (1x1 blue pixel, will be stretched)
        # This is a base64 encoded 1x1 blue PNG
        echo "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA4nEKtAAAAABJRU5ErkJggg==" | base64 -d > "$filename"
        echo "Created placeholder $filename"
    fi
}

# Create icons directory
mkdir -p icons

# Create icons in different sizes
create_icon 16 "icons/icon16.png"
create_icon 32 "icons/icon32.png"
create_icon 48 "icons/icon48.png"
create_icon 128 "icons/icon128.png"

echo "Icon creation complete!"
