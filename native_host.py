#!/usr/bin/env python3
"""
Native messaging host for Browser Jump Chrome extension.
This script handles opening URLs in different browsers on macOS.
"""

import json
import sys
import subprocess
import struct
import os

def send_message(message):
    """Send a message to the Chrome extension."""
    encoded_message = json.dumps(message).encode('utf-8')
    sys.stdout.buffer.write(struct.pack('I', len(encoded_message)))
    sys.stdout.buffer.write(encoded_message)
    sys.stdout.buffer.flush()

def read_message():
    """Read a message from the Chrome extension."""
    raw_length = sys.stdin.buffer.read(4)
    if len(raw_length) == 0:
        return None
    
    message_length = struct.unpack('I', raw_length)[0]
    message = sys.stdin.buffer.read(message_length).decode('utf-8')
    return json.loads(message)

def open_browser_macos(browser_info, url):
    """Open URL in specified browser on macOS."""
    try:
        # Try using the bundle identifier first (most reliable)
        if 'bundleId' in browser_info:
            cmd = ['open', '-b', browser_info['bundleId'], url]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                return True, f"Opened in {browser_info['name']}"
            else:
                # If bundle ID fails, try with application name
                if 'executable' in browser_info:
                    cmd = ['open', '-a', browser_info['executable'], url]
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                    
                    if result.returncode == 0:
                        return True, f"Opened in {browser_info['name']}"
                    else:
                        return False, f"Failed to open {browser_info['name']}: {result.stderr}"
                else:
                    return False, f"Failed to open {browser_info['name']}: {result.stderr}"
        else:
            return False, "No bundle ID or executable specified"
            
    except subprocess.TimeoutExpired:
        return False, f"Timeout opening {browser_info['name']}"
    except Exception as e:
        return False, f"Error opening {browser_info['name']}: {str(e)}"

def handle_message(message):
    """Handle incoming message from Chrome extension."""
    try:
        command = message.get('command')
        
        if command == 'openBrowser':
            browser_info = message.get('browser')
            url = message.get('url')
            
            if not browser_info or not url:
                return {'success': False, 'error': 'Missing browser info or URL'}
            
            # Check if we're on macOS
            if os.name != 'posix' or not sys.platform.startswith('darwin'):
                return {'success': False, 'error': 'This host only supports macOS'}
            
            success, message_text = open_browser_macos(browser_info, url)
            
            return {
                'success': success,
                'message': message_text,
                'browser': browser_info['name'],
                'url': url
            }
        else:
            return {'success': False, 'error': f'Unknown command: {command}'}
            
    except Exception as e:
        return {'success': False, 'error': f'Error processing message: {str(e)}'}

def main():
    """Main loop for native messaging host."""
    try:
        while True:
            message = read_message()
            if message is None:
                break
                
            response = handle_message(message)
            send_message(response)
            
    except Exception as e:
        # Send error message and exit
        send_message({'success': False, 'error': f'Host error: {str(e)}'})
        sys.exit(1)

if __name__ == '__main__':
    main()
