// Background service worker for handling browser opening requests

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'openInBrowser') {
    handleOpenInBrowser(request.browser, request.url)
      .then(result => sendResponse({ success: true, result }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    
    // Return true to indicate we'll send a response asynchronously
    return true;
  }
});

async function handleOpenInBrowser(browser, url) {
  try {
    // For macOS, we'll use the 'open' command with the browser's bundle identifier
    // This requires a native messaging host to execute shell commands
    
    // First, try using the native messaging approach
    if (chrome.runtime.connectNative) {
      return await openWithNativeMessaging(browser, url);
    } else {
      throw new Error('Native messaging not available');
    }
  } catch (error) {
    console.error('Error opening browser:', error);
    throw error;
  }
}

async function openWithNativeMessaging(browser, url) {
  return new Promise((resolve, reject) => {
    try {
      // Connect to native messaging host
      const port = chrome.runtime.connectNative('com.browserjump.host');
      
      port.onMessage.addListener((response) => {
        if (response.success) {
          resolve(response);
        } else {
          reject(new Error(response.error || 'Native messaging failed'));
        }
        port.disconnect();
      });
      
      port.onDisconnect.addListener(() => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        }
      });
      
      // Send the command to open the browser
      port.postMessage({
        command: 'openBrowser',
        browser: browser,
        url: url
      });
      
    } catch (error) {
      reject(error);
    }
  });
}

// Alternative approach using chrome.tabs API to create a new tab and then
// attempt to trigger the default browser behavior (limited functionality)
async function fallbackOpenBrowser(browser, url) {
  // This is a fallback that won't actually open in external browsers
  // but will at least open the URL in a new Chrome tab
  console.log(`Fallback: Opening ${url} in new Chrome tab (${browser.name} not available)`);
  
  try {
    await chrome.tabs.create({ url: url });
    return { success: true, message: `Opened in new Chrome tab (${browser.name} not available)` };
  } catch (error) {
    throw new Error(`Failed to open URL: ${error.message}`);
  }
}
