# Browser Jump Chrome Extension

A Chrome extension that allows you to quickly open the current tab in different browsers with a single click.

## Supported Browsers

- **Safari** - macOS default browser
- **Arc** - Modern browser by The Browser Company
- **Firefox** - Mozilla Firefox
- **Dia** - <PERSON>a browser
- **Polypane** - Multi-viewport browser for developers
- **Slidepad** - Slidepad browser

## Features

- Clean, intuitive popup interface
- One-click browser switching
- Works with Manifest V3
- Native macOS integration
- Visual feedback and status messages

## Installation

### Step 1: Install the Chrome Extension

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" in the top right
3. Click "Load unpacked" and select this directory
4. Note the Extension ID (you'll need it for Step 2)

### Step 2: Install the Native Messaging Host

The extension requires a native messaging host to communicate with the system and open external browsers.

1. Run the installation script:
   ```bash
   ./install_native_host.sh
   ```

2. Update the native messaging host manifest with your extension ID:
   ```bash
   # Edit the file with your extension ID
   nano "$HOME/Library/Application Support/Google/Chrome/NativeMessagingHosts/com.browserjump.host.json"
   ```

3. Replace `YOUR_EXTENSION_ID_HERE` with the actual extension ID from Step 1.

### Step 3: Test the Extension

1. Click the Browser Jump extension icon in Chrome
2. Select a browser from the popup
3. The current tab should open in the selected browser

## How It Works

### Architecture

1. **Chrome Extension**: Provides the user interface and handles tab information
2. **Native Messaging Host**: A Python script that communicates with the extension and opens browsers using macOS system commands
3. **Browser Integration**: Uses macOS `open` command with bundle identifiers for reliable browser launching

### Browser Detection

The extension uses macOS bundle identifiers to open browsers:

- Safari: `com.apple.Safari`
- Arc: `company.thebrowser.Browser`
- Firefox: `org.mozilla.firefox`
- Dia: `com.dia.Dia`
- Polypane: `com.polypane.app`
- Slidepad: `com.slidepad.app`

If a browser isn't installed, the system will show an appropriate error message.

## File Structure

```
chrome-jump-extension/
├── manifest.json                 # Extension manifest (Manifest V3)
├── popup.html                   # Extension popup interface
├── popup.js                     # Popup logic and browser communication
├── background.js                # Service worker for native messaging
├── native_host.py              # Native messaging host (Python)
├── com.browserjump.host.json   # Native messaging host manifest
├── install_native_host.sh      # Installation script
├── icons/                      # Extension icons
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── README.md                   # This file
```

## Troubleshooting

### Extension doesn't appear
- Make sure Developer mode is enabled in Chrome
- Check that all files are present in the extension directory

### Browsers don't open
- Verify the native messaging host is installed correctly
- Check that the extension ID in the native host manifest matches your actual extension ID
- Ensure the target browsers are installed on your system

### Permission errors
- Make sure `native_host.py` is executable: `chmod +x native_host.py`
- Verify the path in the native messaging host manifest is correct

### Debugging

1. Check Chrome extension console:
   - Go to `chrome://extensions/`
   - Click "Details" on Browser Jump
   - Click "Inspect views: service worker"

2. Check native messaging host logs:
   - The native host writes to stderr, which can be viewed in the Chrome extension console

## Development

### Modifying Browser List

To add or modify browsers, update the `BROWSERS` object in `popup.js`:

```javascript
const BROWSERS = {
  newbrowser: {
    name: 'New Browser',
    bundleId: 'com.example.newbrowser',
    executable: 'NewBrowser'
  }
};
```

Then add a corresponding button in `popup.html`.

### Testing

You can test the native messaging host independently:

```bash
echo '{"command":"openBrowser","browser":{"name":"Safari","bundleId":"com.apple.Safari"},"url":"https://example.com"}' | python3 native_host.py
```

## Requirements

- macOS (the native messaging host uses macOS-specific commands)
- Python 3 (for the native messaging host)
- Chrome or Chromium-based browser
- Target browsers must be installed on the system

## License

This project is open source. Feel free to modify and distribute as needed.
