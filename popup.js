// Browser configurations with their bundle identifiers and executable paths
const BROWSERS = {
  safari: {
    name: 'Safari',
    bundleId: 'com.apple.Safari',
    executable: 'Safari'
  },
  arc: {
    name: 'Arc',
    bundleId: 'company.thebrowser.Browser',
    executable: 'Arc'
  },
  firefox: {
    name: 'Firefox',
    bundleId: 'org.mozilla.firefox',
    executable: 'Firefox'
  },
  dia: {
    name: '<PERSON>a',
    bundleId: 'com.dia.Dia',
    executable: 'Dia'
  },
  polypane: {
    name: 'Polypane',
    bundleId: 'com.polypane.app',
    executable: 'Polypane'
  },
  slidepad: {
    name: 'Slidepad',
    bundleId: 'com.slidepad.app',
    executable: 'Slidepad'
  }
};

function showStatus(message, isError = false) {
  const status = document.getElementById('status');
  status.textContent = message;
  status.className = isError ? 'status show error' : 'status show';
  
  setTimeout(() => {
    status.className = 'status';
  }, 3000);
}

async function getCurrentTab() {
  try {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    return tab;
  } catch (error) {
    console.error('Error getting current tab:', error);
    throw error;
  }
}

async function openInBrowser(browserKey, url) {
  const browser = BROWSERS[browserKey];
  if (!browser) {
    throw new Error(`Unknown browser: ${browserKey}`);
  }

  try {
    // Send message to background script to handle the browser opening
    const response = await chrome.runtime.sendMessage({
      action: 'openInBrowser',
      browser: browser,
      url: url
    });

    if (response.success) {
      showStatus(`Opening in ${browser.name}...`);
      // Close popup after a short delay
      setTimeout(() => window.close(), 1000);
    } else {
      throw new Error(response.error || 'Failed to open browser');
    }
  } catch (error) {
    console.error('Error opening browser:', error);
    showStatus(`Error: ${error.message}`, true);
  }
}

// Initialize popup
document.addEventListener('DOMContentLoaded', async () => {
  try {
    // Get current tab URL
    const currentTab = await getCurrentTab();
    const currentUrl = currentTab.url;

    // Add click handlers to browser buttons
    const browserItems = document.querySelectorAll('.browser-item');
    browserItems.forEach(item => {
      item.addEventListener('click', async () => {
        const browserKey = item.dataset.browser;
        await openInBrowser(browserKey, currentUrl);
      });
    });

  } catch (error) {
    console.error('Error initializing popup:', error);
    showStatus('Error loading extension', true);
  }
});
