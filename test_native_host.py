#!/usr/bin/env python3
"""
Test script to verify the native messaging host works correctly.
This simulates what the Chrome extension would send to the native host.
"""

import json
import subprocess
import sys

def test_browser_opening(browser_name, bundle_id, url="https://example.com"):
    """Test opening a browser with the native host."""
    print(f"Testing {browser_name}...")
    
    # Create the message that would be sent by the Chrome extension
    message = {
        "command": "openBrowser",
        "browser": {
            "name": browser_name,
            "bundleId": bundle_id,
            "executable": browser_name
        },
        "url": url
    }
    
    try:
        # Run the native host with the test message
        process = subprocess.Popen(
            ['python3', 'native_host.py'],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        # Send the message in native messaging format
        message_json = json.dumps(message).encode('utf-8')
        length = len(message_json).to_bytes(4, byteorder='little')
        
        stdout, stderr = process.communicate(input=length + message_json, timeout=10)
        
        # Parse the response
        if len(stdout) >= 4:
            response_length = int.from_bytes(stdout[:4], byteorder='little')
            response_json = stdout[4:4+response_length].decode('utf-8')
            response = json.loads(response_json)
            
            if response.get('success'):
                print(f"✅ {browser_name}: {response.get('message', 'Success')}")
            else:
                print(f"❌ {browser_name}: {response.get('error', 'Unknown error')}")
        else:
            print(f"❌ {browser_name}: No response from native host")
            if stderr:
                print(f"   Error: {stderr.decode('utf-8')}")
                
    except subprocess.TimeoutExpired:
        print(f"❌ {browser_name}: Timeout")
        process.kill()
    except Exception as e:
        print(f"❌ {browser_name}: Exception - {str(e)}")

def main():
    """Test all supported browsers."""
    print("Testing Browser Jump Native Host")
    print("=" * 40)
    print("Note: This will attempt to open https://example.com in each browser")
    print("You may see browser windows open during this test.\n")
    
    # Test browsers
    browsers = [
        ("Safari", "com.apple.Safari"),
        ("Arc", "company.thebrowser.Browser"),
        ("Firefox", "org.mozilla.firefox"),
        ("Dia", "com.dia.Dia"),
        ("Polypane", "com.polypane.app"),
        ("Slidepad", "com.slidepad.app")
    ]
    
    for browser_name, bundle_id in browsers:
        test_browser_opening(browser_name, bundle_id)
    
    print("\n" + "=" * 40)
    print("Test complete!")
    print("\nNote: Browsers that aren't installed will show error messages.")
    print("This is expected behavior.")

if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == '--help':
        print("Usage: python3 test_native_host.py")
        print("This script tests the native messaging host by simulating")
        print("browser opening requests from the Chrome extension.")
        sys.exit(0)
    
    main()
