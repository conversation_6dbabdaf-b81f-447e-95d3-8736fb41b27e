#!/usr/bin/env python3
"""
Simple test script to verify the Chrome extension structure and files.
"""

import json
import os
import sys

def test_manifest():
    """Test that manifest.json is valid and has required fields."""
    print("Testing manifest.json...")
    
    if not os.path.exists('manifest.json'):
        print("❌ manifest.json not found")
        return False
    
    try:
        with open('manifest.json', 'r') as f:
            manifest = json.load(f)
        
        required_fields = ['manifest_version', 'name', 'version', 'permissions', 'action']
        for field in required_fields:
            if field not in manifest:
                print(f"❌ Missing required field: {field}")
                return False
        
        if manifest['manifest_version'] != 3:
            print("❌ Not using Manifest V3")
            return False
        
        print("✅ manifest.json is valid")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in manifest.json: {e}")
        return False

def test_files():
    """Test that all required files exist."""
    print("\nTesting required files...")
    
    required_files = [
        'popup.html',
        'popup.js',
        'background.js',
        'native_host.py',
        'com.browserjump.host.json',
        'icons/icon16.png',
        'icons/icon32.png',
        'icons/icon48.png',
        'icons/icon128.png'
    ]
    
    all_exist = True
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} not found")
            all_exist = False
    
    return all_exist

def test_native_host_manifest():
    """Test that native host manifest is valid."""
    print("\nTesting native host manifest...")
    
    if not os.path.exists('com.browserjump.host.json'):
        print("❌ Native host manifest not found")
        return False
    
    try:
        with open('com.browserjump.host.json', 'r') as f:
            manifest = json.load(f)
        
        required_fields = ['name', 'description', 'path', 'type', 'allowed_origins']
        for field in required_fields:
            if field not in manifest:
                print(f"❌ Missing required field in native host manifest: {field}")
                return False
        
        if not os.path.exists(manifest['path']):
            print(f"❌ Native host path doesn't exist: {manifest['path']}")
            return False
        
        if not os.access(manifest['path'], os.X_OK):
            print(f"❌ Native host is not executable: {manifest['path']}")
            return False
        
        print("✅ Native host manifest is valid")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in native host manifest: {e}")
        return False

def main():
    """Run all tests."""
    print("Browser Jump Extension Test Suite")
    print("=" * 40)
    
    tests = [
        test_manifest,
        test_files,
        test_native_host_manifest
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print("\n" + "=" * 40)
    if all(results):
        print("🎉 All tests passed! Extension is ready to install.")
        print("\nNext steps:")
        print("1. Load the extension in Chrome (chrome://extensions/)")
        print("2. Run ./install_native_host.sh")
        print("3. Update the extension ID in the native host manifest")
    else:
        print("❌ Some tests failed. Please fix the issues above.")
        sys.exit(1)

if __name__ == '__main__':
    main()
