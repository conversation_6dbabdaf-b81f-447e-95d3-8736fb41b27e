#!/bin/bash

# Installation script for Browser Jump native messaging host

set -e

EXTENSION_DIR="$(pwd)"
HOST_NAME="com.browserjump.host"
MANIFEST_FILE="$HOST_NAME.json"
NATIVE_HOST_DIR="$HOME/Library/Application Support/Google/Chrome/NativeMessagingHosts"

echo "Installing Browser Jump native messaging host..."

# Create the native messaging hosts directory if it doesn't exist
mkdir -p "$NATIVE_HOST_DIR"

# Update the path in the manifest file
sed "s|/Users/<USER>/Sites/chrome-jump-extension|$EXTENSION_DIR|g" "$MANIFEST_FILE" > "$NATIVE_HOST_DIR/$MANIFEST_FILE"

echo "Native messaging host installed to: $NATIVE_HOST_DIR/$MANIFEST_FILE"
echo ""
echo "Next steps:"
echo "1. Load the Chrome extension in Developer Mode"
echo "2. Copy the extension ID from chrome://extensions/"
echo "3. Update the allowed_origins in: $NATIVE_HOST_DIR/$MANIFEST_FILE"
echo "   Replace YOUR_EXTENSION_ID_HERE with your actual extension ID"
echo ""
echo "Example:"
echo '  "allowed_origins": ['
echo '    "chrome-extension://abcdefghijklmnopqrstuvwxyz123456/"'
echo '  ]'
echo ""
echo "Installation complete!"
