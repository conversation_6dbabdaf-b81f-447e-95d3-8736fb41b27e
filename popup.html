<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 250px;
      padding: 0;
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f8f9fa;
    }
    
    .header {
      background: #4285f4;
      color: white;
      padding: 12px 16px;
      font-weight: 500;
      font-size: 14px;
    }
    
    .browser-list {
      padding: 8px 0;
    }
    
    .browser-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      cursor: pointer;
      transition: background-color 0.2s;
      border: none;
      background: none;
      width: 100%;
      text-align: left;
      font-size: 14px;
    }
    
    .browser-item:hover {
      background: #e8f0fe;
    }
    
    .browser-icon {
      width: 20px;
      height: 20px;
      margin-right: 12px;
      border-radius: 4px;
    }
    
    .browser-name {
      color: #202124;
      font-weight: 400;
    }
    
    .status {
      padding: 8px 16px;
      font-size: 12px;
      color: #5f6368;
      text-align: center;
      display: none;
    }
    
    .status.show {
      display: block;
    }
  </style>
</head>
<body>
  <div class="header">
    Jump to Browser
  </div>
  
  <div class="browser-list">
    <button class="browser-item" data-browser="safari">
      <div class="browser-icon" style="background: linear-gradient(135deg, #007AFF, #5AC8FA);">🧭</div>
      <span class="browser-name">Safari</span>
    </button>
    
    <button class="browser-item" data-browser="arc">
      <div class="browser-icon" style="background: linear-gradient(135deg, #FF6B6B, #4ECDC4);">🌈</div>
      <span class="browser-name">Arc</span>
    </button>
    
    <button class="browser-item" data-browser="firefox">
      <div class="browser-icon" style="background: linear-gradient(135deg, #FF9500, #FF6000);">🦊</div>
      <span class="browser-name">Firefox</span>
    </button>
    
    <button class="browser-item" data-browser="dia">
      <div class="browser-icon" style="background: linear-gradient(135deg, #8E44AD, #3498DB);">💎</div>
      <span class="browser-name">Dia</span>
    </button>
    
    <button class="browser-item" data-browser="polypane">
      <div class="browser-icon" style="background: linear-gradient(135deg, #2ECC71, #27AE60);">📱</div>
      <span class="browser-name">Polypane</span>
    </button>
    
    <button class="browser-item" data-browser="slidepad">
      <div class="browser-icon" style="background: linear-gradient(135deg, #E74C3C, #C0392B);">📋</div>
      <span class="browser-name">Slidepad</span>
    </button>
  </div>
  
  <div class="status" id="status"></div>
  
  <script src="popup.js"></script>
</body>
</html>
